import path from "path";
/// <reference types="vitest" />
import { cloudflareDevProxyVitePlugin, vitePlugin as remix } from "@remix-run/dev";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import { getLoadContext } from "./load-context";

declare module "@remix-run/cloudflare" {
  interface Future {
    v3_singleFetch: false;
  }
}

export default defineConfig({
  // Exclude .000 directory from all processing
  resolve: {
    alias: {
      "~": path.resolve(__dirname, "./app"),
    },
    mainFields: ["browser", "module", "main"],
  },
  plugins: [
    cloudflareDevProxyVitePlugin({
      getLoadContext: getLoadContext as (args: any) => any,
    }),
    remix({
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
        v3_singleFetch: false,
        v3_lazyRouteDiscovery: true,
      },
      // Exclude .000 directory from Remix processing
      ignoredRouteFiles: ["**/.*", "**/*.css", "**/*.test.{js,jsx,ts,tsx}", ".000/**/*"],
    }),
    tsconfigPaths({
      // Exclude .000 directory from TypeScript path resolution
      projects: ["./tsconfig.json"],
      ignoreConfigErrors: true,
    }),
    // Bundle analyzer - only when ANALYZE=true (disabled for Cloudflare Workers)
    // Note: Bundle analyzer is disabled for Cloudflare Workers compatibility
    // ...(process.env.ANALYZE === "true" ? [analyzer({ analyzerMode: "server", openAnalyzer: true })] : []),
  ],
  ssr: {
    resolve: {
      conditions: ["workerd", "worker", "browser"],
    },
    // noExternal: ["@stackframe/react"], // Temporarily disabled to avoid global scope issues
  },
  optimizeDeps: {
    // include: ["@stackframe/react"], // Temporarily disabled to avoid global scope issues
    exclude: [],
  },
  build: {
    minify: true,
    // Enhanced build configuration for performance and smaller bundles
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching and smaller bundles
        manualChunks: (id) => {
          // Only apply manual chunks for client build
          if (id.includes("node_modules")) {
            // Core React libraries
            if (id.includes("react") || id.includes("react-dom")) {
              return "vendor";
            }
            // UI components
            if (id.includes("@radix-ui")) {
              return "ui";
            }
            // Utility libraries
            if (
              id.includes("clsx") ||
              id.includes("tailwind-merge") ||
              id.includes("class-variance-authority")
            ) {
              return "utils";
            }
            // AI SDKs - separate chunk for better caching
            if (id.includes("@ai-sdk") || id.includes("openai") || id.includes("ai/")) {
              return "ai";
            }
            // Keystatic CMS - separate chunk
            if (id.includes("@keystatic")) {
              return "keystatic";
            }
            // Icons
            if (id.includes("lucide-react")) {
              return "icons";
            }
            // Payment libraries
            if (id.includes("stripe")) {
              return "payments";
            }
            // Other large libraries
            if (id.includes("zod") || id.includes("drizzle-orm")) {
              return "validation";
            }
          }
        },
      },
      // External dependencies for Cloudflare Workers
      external: (id) => {
        // Don't bundle Node.js built-ins
        return id.startsWith("node:");
      },
    },
    // Disable source maps in production for smaller bundles
    sourcemap: false,
    // Chunk size warning limit
    chunkSizeWarningLimit: 500,
    // Tree shaking configuration
    treeshake: {
      moduleSideEffects: false,
      propertyReadSideEffects: false,
      unknownGlobalSideEffects: false,
    },
  },
});
