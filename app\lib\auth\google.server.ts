/**
 * Google Authentication Handler
 * Handles Google One Tap ID Token verification and user creation
 */

import { eq } from "drizzle-orm";
import { createDbFromEnv } from "~/lib/db";
import { sessions, users } from "~/lib/db/schema";
import { createTokenPair, getUserAgent, getUser<PERSON> } from "./jwt.server";
import { getUuid } from "./hash";

// Google ID Token payload interface
export interface GoogleIdTokenPayload {
  iss: string; // Issuer
  aud: string; // Audience (your Google Client ID)
  sub: string; // Subject (Google user ID)
  email: string;
  email_verified: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  iat: number; // Issued at
  exp: number; // Expires at
}

/**
 * Verify Google ID Token
 */
export async function verifyGoogleIdToken(
  idToken: string,
  clientId: string
): Promise<GoogleIdTokenPayload | null> {
  try {
    // Use Google's tokeninfo endpoint to verify the token
    const response = await fetch(`https://oauth2.googleapis.com/tokeninfo?id_token=${idToken}`);

    if (!response.ok) {
      console.error("Google token verification failed:", response.status);
      return null;
    }

    const payload = (await response.json()) as GoogleIdTokenPayload;

    // Verify the token is for our application
    if (payload.aud !== clientId) {
      console.error("Token audience mismatch");
      return null;
    }

    // Verify the token hasn't expired
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp < now) {
      console.error("Token has expired");
      return null;
    }

    // Verify email is verified
    if (!payload.email_verified) {
      console.error("Email not verified");
      return null;
    }

    return payload;
  } catch (error) {
    console.error("Error verifying Google ID token:", error);
    return null;
  }
}

/**
 * Handle Google authentication
 * Creates or updates user and creates session
 */
export async function handleGoogleAuth(
  idToken: string,
  request: Request,
  env?: Record<string, string | undefined>
): Promise<{
  success: boolean;
  user?: any;
  accessToken?: string;
  refreshToken?: string;
  error?: string;
}> {
  try {
    const clientId = env?.GOOGLE_CLIENT_ID || process.env.GOOGLE_CLIENT_ID;
    if (!clientId) {
      return {
        success: false,
        error: "Google Client ID not configured",
      };
    }

    // Verify the Google ID token
    const googlePayload = await verifyGoogleIdToken(idToken, clientId);
    if (!googlePayload) {
      return {
        success: false,
        error: "Invalid Google ID token",
      };
    }

    const db = createDbFromEnv(env);

    // Check if user already exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.email, googlePayload.email))
      .limit(1);

    let user;

    if (existingUser.length > 0) {
      // Update existing user
      user = existingUser[0];

      // Update user info from Google
      await db
        .update(users)
        .set({
          name: googlePayload.name,
          avatar: googlePayload.picture,
          signinType: "oauth",
          signinProvider: "google",
          signinOpenid: googlePayload.sub,
          signinIp: getUserIP(request),
          updatedAt: new Date(),
        })
        .where(eq(users.id, user.id));
    } else {
      // Create new user
      const newUserData = {
        id: getUuid(),
        uuid: getUuid(),
        name: googlePayload.name,
        email: googlePayload.email,
        avatar: googlePayload.picture,
        credits: 100, // Give new users some initial credits
        signinType: "oauth",
        signinProvider: "google",
        signinOpenid: googlePayload.sub,
        signinIp: getUserIP(request),
        isAffiliate: false,
      };

      const insertResult = await db.insert(users).values(newUserData).returning();

      if (insertResult.length === 0) {
        return {
          success: false,
          error: "Failed to create user",
        };
      }

      user = insertResult[0];

      // Process new user onboarding
      try {
        const { processNewUserOnboarding } = await import("~/services/onboarding.server");
        await processNewUserOnboarding(
          {
            id: user.id,
            uuid: user.uuid,
            name: user.name,
            email: user.email,
            inviteCode: user.inviteCode,
            invitedBy: user.invitedBy,
          },
          db,
          env
        );
      } catch (error) {
        console.error("Onboarding process failed:", error);
        // Continue with authentication even if onboarding fails
      }
    }

    // Create session
    const sessionData = {
      id: getUuid(),
      userId: user.id,
      sessionToken: getUuid(),
      refreshToken: getUuid(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      refreshExpiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      userAgent: getUserAgent(request),
      ipAddress: getUserIP(request),
      isActive: true,
    };

    const sessionResult = await db.insert(sessions).values(sessionData).returning();

    if (sessionResult.length === 0) {
      return {
        success: false,
        error: "Failed to create session",
      };
    }

    const session = sessionResult[0];

    // Create JWT tokens
    const tokens = await createTokenPair(
      {
        id: user.id,
        uuid: user.uuid,
        email: user.email,
        name: user.name,
        avatar: user.avatar || undefined,
      },
      session.id
    );

    // Return relevant user information
    return {
      success: true,
      user: {
        id: user.id,
        uuid: user.uuid,
        email: user.email,
        name: user.name,
        avatar: user.avatar,
        credits: user.credits,
      },
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
    };
  } catch (error) {
    console.error("Google authentication error:", error);
    return {
      success: false,
      error: "Authentication failed",
    };
  }
}

/**
 * Generate invite code for new users
 */
function generateInviteCode(): string {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = "";
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Handle user registration with invite code
 */
export async function handleUserRegistration(
  googlePayload: GoogleIdTokenPayload,
  inviteCode?: string,
  env?: Record<string, string | undefined>
): Promise<any> {
  const db = createDbFromEnv(env);

  let invitedBy = null;
  let bonusCredits = 0;

  // Check if invite code is valid
  if (inviteCode) {
    const inviter = await db.select().from(users).where(eq(users.inviteCode, inviteCode)).limit(1);

    if (inviter.length > 0) {
      invitedBy = inviter[0].uuid;
      bonusCredits = 50; // Bonus credits for using invite code
    }
  }

  const newUserData = {
    id: getUuid(),
    uuid: getUuid(),
    name: googlePayload.name,
    email: googlePayload.email,
    avatar: googlePayload.picture,
    credits: 100 + bonusCredits, // Base credits + invite bonus
    inviteCode: generateInviteCode(),
    invitedBy,
    theme: "system", // Default theme
    signinType: "oauth",
    signinProvider: "google",
    signinOpenid: googlePayload.sub,
    isAffiliate: false,
  };

  const insertResult = await db.insert(users).values(newUserData).returning();

  return insertResult[0];
}
