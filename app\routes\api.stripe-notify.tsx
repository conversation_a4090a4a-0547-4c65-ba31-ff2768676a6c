import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { eq } from "drizzle-orm";
import type <PERSON><PERSON> from "stripe";
import { getUuid } from "~/lib/auth/hash";
import { respOk, respServerErr } from "~/lib/api/resp";
import type { Database } from "~/lib/db/db";
import { subscriptionItems as subscriptionItemsTable, subscriptions, users } from "~/lib/db/schema";
import { getStripeClient } from "~/lib/payment/stripe.server";
import { CreditsTransType, increaseCredits } from "~/services/credit";
import {
  notifyCreditsAdded,
  notifyPaymentFailed,
  notifyPaymentSuccess,
} from "~/services/notification-helpers.server";
import { handleOrderSession } from "~/services/order";
import { getSubscriptionPlans } from "~/services/subscription.server";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respServerErr("Method not allowed");
  }

  try {
    const db = context.db;
    if (!db) {
      return respServerErr("Database not available");
    }

    const stripeSecretKey = context.cloudflare.env.STRIPE_SECRET_KEY;
    const stripeWebhookSecret = context.cloudflare.env.STRIPE_WEBHOOK_SECRET;

    if (!stripeSecretKey || !stripeWebhookSecret) {
      throw new Error("invalid stripe config");
    }

    const stripe = getStripeClient(stripeSecretKey);

    const signature = request.headers.get("stripe-signature");
    const body = await request.text();

    if (!signature || !body) {
      throw new Error("invalid notify data");
    }

    const event = stripe.webhooks.constructEvent(body, signature, stripeWebhookSecret);

    console.log("stripe notify event: ", event.type);

    switch (event.type) {
      case "checkout.session.completed": {
        const session = event.data.object as Stripe.Checkout.Session;
        if (session.mode === "subscription") {
          await handleSubscriptionSession(session, db);
        } else {
          await handleOrderSession(session, db);
        }
        break;
      }

      case "customer.subscription.created": {
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionCreated(subscription, db);
        break;
      }

      case "customer.subscription.updated": {
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionUpdated(subscription, db);
        break;
      }

      case "customer.subscription.deleted": {
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionDeleted(subscription, db);
        break;
      }

      case "invoice.payment_succeeded": {
        const invoice = event.data.object as Stripe.Invoice;
        await handleInvoicePaymentSucceeded(invoice, db);
        break;
      }

      case "invoice.payment_failed": {
        const invoice = event.data.object as Stripe.Invoice;
        await handleInvoicePaymentFailed(invoice, db);
        break;
      }

      default:
        console.log("not handled event: ", event.type);
    }

    return respOk();
  } catch (e: any) {
    console.log("stripe notify failed: ", e);
    return respServerErr(`handle stripe notify failed: ${e.message}`);
  }
}

/**
 * Handle subscription checkout session completion
 */
async function handleSubscriptionSession(session: Stripe.Checkout.Session, db: Database) {
  try {
    const userUuid = session.metadata?.user_uuid;
    const planId = session.metadata?.plan_id;

    if (!userUuid || !planId) {
      console.error("Missing user_uuid or plan_id in session metadata");
      return;
    }

    console.log(`Processing subscription session for user ${userUuid}, plan ${planId}`);

    // The actual subscription will be created via the customer.subscription.created webhook
    // This is just for logging and initial processing
  } catch (error) {
    console.error("Error handling subscription session:", error);
  }
}

/**
 * Handle subscription creation
 */
async function handleSubscriptionCreated(subscription: Stripe.Subscription, db: Database) {
  try {
    const userUuid = subscription.metadata?.user_uuid;
    const planId = subscription.metadata?.plan_id;

    if (!userUuid || !planId) {
      console.error("Missing user_uuid or plan_id in subscription metadata");
      return;
    }

    const plans = getSubscriptionPlans();
    const plan = plans.find((p) => p.id === planId);

    if (!plan) {
      console.error(`Plan not found: ${planId}`);
      return;
    }

    // Create subscription record
    const subscriptionRecord = await db
      .insert(subscriptions)
      .values({
        id: getUuid(),
        accountId: userUuid,
        stripeSubscriptionId: subscription.id,
        stripeCustomerId: subscription.customer as string,
        status: subscription.status,
        billingProvider: "stripe",
        active: subscription.status === "active",
        currency: subscription.currency || "usd",
        periodStartsAt: new Date(subscription.current_period_start * 1000),
        periodEndsAt: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
      })
      .returning();

    // Create subscription items
    for (const item of subscription.items.data) {
      await db.insert(subscriptionItemsTable).values({
        id: getUuid(),
        subscriptionId: subscriptionRecord[0].id,
        stripeSubscriptionItemId: item.id,
        stripePriceId: item.price.id,
        productId: planId,
        variantId: planId, // Use planId as variantId for now
        quantity: item.quantity || 1,
        interval: plan.interval,
        intervalCount: 1,
      });
    }

    // Add initial credits
    await increaseCredits(
      {
        user_uuid: userUuid,
        trans_type: CreditsTransType.Purchase,
        credits: plan.credits,
        description: `Subscription credits for ${plan.name}`,
      },
      db
    );

    // Send payment success notification
    await notifyPaymentSuccess(
      userUuid,
      {
        amount: (plan.price / 100).toFixed(2),
        currency: subscription.currency || "usd",
        planName: plan.name,
        creditsAdded: plan.credits,
        nextBillingDate: new Date(subscription.current_period_end * 1000).toLocaleDateString(),
        subscriptionUrl: "/console/subscription",
      },
      db
    );

    console.log(`Subscription created for user ${userUuid}: ${subscription.id}`);
  } catch (error) {
    console.error("Error handling subscription creation:", error);
  }
}

/**
 * Handle subscription updates
 */
async function handleSubscriptionUpdated(subscription: Stripe.Subscription, db: Database) {
  try {
    await db
      .update(subscriptions)
      .set({
        status: subscription.status,
        active: subscription.status === "active",
        periodStartsAt: new Date(subscription.current_period_start * 1000),
        periodEndsAt: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        updatedAt: new Date(),
      })
      .where(eq(subscriptions.stripeSubscriptionId, subscription.id));

    console.log(`Subscription updated: ${subscription.id}`);
  } catch (error) {
    console.error("Error handling subscription update:", error);
  }
}

/**
 * Handle subscription deletion
 */
async function handleSubscriptionDeleted(subscription: Stripe.Subscription, db: Database) {
  try {
    await db
      .update(subscriptions)
      .set({
        status: "canceled",
        active: false,
        updatedAt: new Date(),
      })
      .where(eq(subscriptions.stripeSubscriptionId, subscription.id));

    console.log(`Subscription deleted: ${subscription.id}`);
  } catch (error) {
    console.error("Error handling subscription deletion:", error);
  }
}

/**
 * Handle successful invoice payment
 */
async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice, db: Database) {
  try {
    if (!invoice.subscription) {
      return; // Not a subscription invoice
    }

    const subscription = await db
      .select()
      .from(subscriptions)
      .where(eq(subscriptions.stripeSubscriptionId, invoice.subscription as string))
      .limit(1);

    if (!subscription || subscription.length === 0) {
      console.error(`Subscription not found for invoice: ${invoice.id}`);
      return;
    }

    const userUuid = subscription[0].accountId;

    // Add credits for recurring payment (if not the first payment)
    if (invoice.billing_reason === "subscription_cycle") {
      const subscriptionItemsData = await db
        .select()
        .from(subscriptionItemsTable)
        .where(eq(subscriptionItemsTable.subscriptionId, subscription[0].id));

      if (subscriptionItemsData.length > 0) {
        const planId = subscriptionItemsData[0].productId;
        const plans = getSubscriptionPlans();
        const plan = plans.find((p) => p.id === planId);

        if (plan) {
          await increaseCredits(
            {
              user_uuid: userUuid,
              trans_type: CreditsTransType.Purchase,
              credits: plan.credits,
              description: `Monthly subscription credits for ${plan.name}`,
            },
            db
          );

          // Send credits added notification for recurring payments
          await notifyCreditsAdded(
            userUuid,
            {
              creditsAdded: plan.credits,
              reason: `Monthly subscription renewal for ${plan.name}`,
              newBalance: 0, // TODO: Get actual balance
            },
            db
          );
        }
      }
    }

    console.log(`Invoice payment succeeded: ${invoice.id}`);
  } catch (error) {
    console.error("Error handling invoice payment success:", error);
  }
}

/**
 * Handle failed invoice payment
 */
async function handleInvoicePaymentFailed(invoice: Stripe.Invoice, db: Database) {
  try {
    console.log(`Invoice payment failed: ${invoice.id}`);

    if (!invoice.subscription) {
      return; // Not a subscription invoice
    }

    const subscription = await db
      .select()
      .from(subscriptions)
      .where(eq(subscriptions.stripeSubscriptionId, invoice.subscription as string))
      .limit(1);

    if (!subscription || subscription.length === 0) {
      console.error(`Subscription not found for failed invoice: ${invoice.id}`);
      return;
    }

    const userUuid = subscription[0].accountId;

    // Get subscription plan details
    const subscriptionItemsData = await db
      .select()
      .from(subscriptionItemsTable)
      .where(eq(subscriptionItemsTable.subscriptionId, subscription[0].id));

    if (subscriptionItemsData.length > 0) {
      const planId = subscriptionItemsData[0].productId;
      const plans = getSubscriptionPlans();
      const plan = plans.find((p) => p.id === planId);

      if (plan) {
        // Send payment failed notification
        await notifyPaymentFailed(
          userUuid,
          {
            amount: (plan.price / 100).toFixed(2),
            currency: invoice.currency || "usd",
            planName: plan.name,
            retryUrl: "/console/subscription",
            supportUrl: "/support",
          },
          db
        );
      }
    }
  } catch (error) {
    console.error("Error handling invoice payment failure:", error);
  }
}
